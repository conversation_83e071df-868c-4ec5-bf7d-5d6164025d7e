import { Inject, Injectable } from '@nestjs/common';
import { ConfigKeys, ConfigService } from '../../../../config/config.service';
import { TransactionService } from '../../../../database/common/transactions.service';
import { CustomerContacts } from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { CommentThreadMapsRepository } from '../../../../database/entities/mappings/repositories/comment-thread-maps.repository';
import { SlackMessagesRepository } from '../../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { CreateNewComment } from '../../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { Field } from '../../../blocks/components/composite/form-builder/conditional-form-builder.composite';
import { SlackAppManagementService } from '../../../core';
import { SlackView } from '../../../decorators/slack-view.decorator';
import { DecoratedSlackViewMiddlewareArgs } from '../../../event-handlers';
import { SlackActionHandler } from '../../../interfaces/action-handler.interface';
import { ViewSubmissionPayload } from '../../../interfaces/slack-view-submission.interface';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { FormBuilderService } from '../../../services/form-builder.service';
import { FormSubmissionService } from '../../../services/form-submission.service';
import { NotificationService } from '../../../services/notification.service';
import { isValidEmail } from '../../../utils/field-validation.utils';
import { validateFieldValue } from '../../../utils/form-field-validation.utils';
import { FORM_FIELD_BLOCK_ID_PREFIX } from './form-field-action.handler';

// Constants
export const FORM_SUBMISSION_MODAL_CALLBACK_ID = 'form_submission_modal';
const LOG_SPAN = 'FormSubmissionHandler';

// Field mapping definitions
const FIELD_MAPPINGS: Record<string, string> = {
  priority: 'priorityId',
  status: 'statusId',
  requester: 'requestorEmail',
  requestor: 'requestorEmail',
  submitter: 'submitterEmail',
  agent: 'assignedAgentId',
  assignee: 'assignedAgentId',
  'assigned agent': 'assignedAgentId',
  'assigned to': 'assignedAgentId',
  subject: 'title',
  details: 'description',
  account: 'accountId',
  type: 'typeId',
  sentiment: 'sentimentId',
  due: 'dueDate',
  'due date': 'dueDate',
};

interface TicketData {
  id: string;
  title: string;
  teamIdentifier?: string;
  ticketId?: string;
  status?: string;
  statusId?: string;
  priority?: string;
  priorityId?: string;
  teamId?: string;
  ticketTeamId?: string;
}

/**
 * Handles form submission view events
 * Processes form data and creates tickets
 */
@Injectable()
@SlackView(FORM_SUBMISSION_MODAL_CALLBACK_ID)
export class FormSubmissionHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly formSubmissionService: FormSubmissionService,
    private readonly formBuilderService: FormBuilderService,

    // External providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly notificationService: NotificationService,

    // Database repositories
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly channelsRepository: ChannelsRepository,
    private readonly commentThreadMapsRepository: CommentThreadMapsRepository,

    // Core services
    private readonly transactionService: TransactionService,
    private readonly slackAppManagementService: SlackAppManagementService,
    private readonly slackApiProvider: SlackWebAPIService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Handles form submission
   * Validates form data and creates a ticket
   */
  async handle(args: DecoratedSlackViewMiddlewareArgs) {
    this.logger.log(`${LOG_SPAN} Handling form submission`);

    try {
      const viewAction = args.body as unknown as ViewSubmissionPayload;
      const metadata = JSON.parse(viewAction.view.private_metadata);
      const { formId, teamId, channelId } = metadata;
      const { installation } = args.context;

      // Fetch the form definition
      const formData = await this.fetchFormDefinition(
        installation,
        formId,
        teamId,
      );
      if (!formData) {
        return this.createErrorResponse(
          'block_0',
          'Failed to load form definition. Please try again.',
        );
      }

      // Process and validate form submission
      const { hasErrors, errors, finalFormData } =
        await this.processAndValidateSubmission(viewAction, formData.fields);

      if (hasErrors) {
        return { response_action: 'errors', errors };
      }

      // Prepare custom fields and standard fields
      const { customFieldValues, standardFields } = this.prepareFormFields(
        finalFormData,
        formData.fields,
      );

      // Follow the same pattern as createTicketDirectly
      

      // Create the ticket
      return await this.createTicket(
        viewAction,
        metadata,
        installation,
        finalFormData,
        customFieldValues,
        standardFields,
        channelId,
        teamId,
        formId,
        args.context.client,
      );
    } catch (error) {
      this.logError(error);
      // Let Bolt handle the error response if it's a systemic issue
      throw error;
    }
  }

  /**
   * Fetches form definition from the service
   */
  private async fetchFormDefinition(
    installation: any,
    formId: string,
    teamId: string,
  ) {
    const fullFormData = await this.formBuilderService.getFormById(
      installation,
      formId,
      teamId,
    );

    if (!fullFormData || !fullFormData.fields) {
      this.logger.error(
        `${LOG_SPAN} Failed to fetch form definition for ID: ${formId}`,
      );
      return null;
    }

    return fullFormData;
  }

  /**
   * Creates a standardized error response
   */
  private createErrorResponse(blockId: string, message: string) {
    return {
      response_action: 'errors',
      errors: {
        [blockId]: message,
      },
    };
  }

  /**
   * Processes and validates form submission
   */
  private async processAndValidateSubmission(
    viewAction: ViewSubmissionPayload,
    fields: Field[],
  ) {
    // Process form submission
    const submissions = this.formSubmissionService.processSubmission(
      viewAction,
      fields,
    );

    // Validate using multiple methods
    const validationErrors =
      this.formSubmissionService.validateSubmission(submissions);
    const fieldErrors = this.validateFieldByField(submissions);

    // Combine validation results
    if (validationErrors.length > 0 || Object.keys(fieldErrors).length > 0) {
      const combinedErrors = this.combineValidationErrors(
        validationErrors,
        fieldErrors,
        fields,
      );
      return { hasErrors: true, errors: combinedErrors, finalFormData: {} };
    }

    // Convert submissions to form data
    const finalFormData = this.convertSubmissionsToFormData(submissions);

    // Validate required fields
    const { hasErrors, errors } = this.validateRequiredFields(
      finalFormData,
      fields,
    );
    if (hasErrors) {
      return { hasErrors, errors, finalFormData };
    }

    return { hasErrors: false, errors: {}, finalFormData };
  }

  /**
   * Validates each field individually
   */
  private validateFieldByField(submissions: any[]) {
    const fieldErrors: Record<string, string> = {};

    for (const submission of submissions) {
      const { field, value } = submission;
      const validationResult = validateFieldValue(field, value);

      if (!validationResult.isValid && validationResult.errorMessage) {
        fieldErrors[`${FORM_FIELD_BLOCK_ID_PREFIX}${field.id}`] =
          validationResult.errorMessage;
        this.logger.debug(
          `${LOG_SPAN} Field validation error for ${field.id} (${field.name}): ${validationResult.errorMessage}`,
        );
      }
    }

    return fieldErrors;
  }

  /**
   * Combines different validation errors into a single response
   */
  private combineValidationErrors(
    validationErrors: string[],
    fieldErrors: Record<string, string>,
    fields: Field[],
  ) {
    const combinedErrors = validationErrors.reduce(
      (acc, error) => {
        const field = fields.find((f: Field) => error.startsWith(f.name));
        if (field) {
          // Ensure block_id corresponds to how blocks are rendered
          const blockId = `${FORM_FIELD_BLOCK_ID_PREFIX}${field.id}`;
          acc[blockId] = acc[blockId] ? `${acc[blockId]}; ${error}` : error;
        } else {
          // Generic error if field not found
          acc.block_0 = `${acc.block_0 ? `${acc.block_0}; ` : ''}${error}`;
        }
        return acc;
      },
      { ...fieldErrors } as Record<string, string>,
    );

    this.logger.debug(
      `${LOG_SPAN} Combined validation errors: ${JSON.stringify(combinedErrors)}`,
    );

    return combinedErrors;
  }

  /**
   * Converts form submissions to structured form data
   */
  private convertSubmissionsToFormData(submissions: any[]) {
    return submissions.reduce(
      (acc, sub) => {
        const fieldId = sub.field.id;
        const fieldNameLower = sub.field.name.toLowerCase();

        if (fieldId.startsWith('CF')) {
          acc[`custom_${fieldId}`] = sub.value;
        } else {
          const mappedFieldName =
            FIELD_MAPPINGS[fieldNameLower] || fieldNameLower;
          acc[mappedFieldName] = sub.value;
        }
        return acc;
      },
      {} as Record<string, any>,
    );
  }

  /**
   * Validates required fields in the form data
   */
  private validateRequiredFields(
    finalFormData: Record<string, any>,
    fields: Field[],
  ) {
    const errors: Record<string, string> = {};
    let hasErrors = false;

    // Check for title
    if (!finalFormData.title) {
      this.logger.error(`${LOG_SPAN} Missing required title field`);
      const titleField = fields.find(
        (f) =>
          (FIELD_MAPPINGS[f.name.toLowerCase()] || f.name.toLowerCase()) ===
          'title',
      );
      errors[
        titleField ? `${FORM_FIELD_BLOCK_ID_PREFIX}${titleField.id}` : 'block_0'
      ] = 'This field is required';
      hasErrors = true;
    }

    // Check for requestor email
    if (
      !this.validateRequestorEmail(finalFormData.requestorEmail, fields, errors)
    ) {
      hasErrors = true;
    }

    return { hasErrors, errors };
  }

  /**
   * Validates the requestor email field
   */
  private validateRequestorEmail(
    email: string | undefined,
    fields: Field[],
    errors: Record<string, string>,
  ): boolean {
    if (!email) {
      this.logger.error(`${LOG_SPAN} Missing required requestorEmail field`);
      const requestorField = fields.find(
        (f) =>
          (FIELD_MAPPINGS[f.name.toLowerCase()] || f.name.toLowerCase()) ===
          'requestorEmail',
      );
      const errorKey = requestorField
        ? `${FORM_FIELD_BLOCK_ID_PREFIX}${requestorField.id}`
        : 'block_1';

      errors[errorKey] =
        `${errors[errorKey] ? `${errors[errorKey]}; ` : ''}Requestor email is required`;
      return false;
    }

    if (typeof email !== 'string') {
      return true; // Not a string, can't validate format
    }

    const requestorField = fields.find(
      (f) =>
        (FIELD_MAPPINGS[f.name.toLowerCase()] || f.name.toLowerCase()) ===
        'requestorEmail',
    );
    const errorKey = requestorField
      ? `${FORM_FIELD_BLOCK_ID_PREFIX}${requestorField.id}`
      : 'block_1';

    // Use the shared email validation utility
    const validation = isValidEmail(email);
    if (!validation.isValid) {
      this.logger.error(`${LOG_SPAN} Invalid requestorEmail format: ${email}`);
      errors[errorKey] =
        validation.errorMessage || 'Please enter a valid email address';
      return false;
    }

    return true;
  }

  /**
   * Prepares form fields for API submission
   */
  private prepareFormFields(
    finalFormData: Record<string, any>,
    fields: Field[],
  ) {
    // Process custom fields
    const customFieldValues = this.processCustomFields(finalFormData, fields);

    // Process standard fields (non-custom, non-reserved)
    const reservedFields = ['requestoremail', 'teamid', 'formid', 'metadata'];
    const standardFields = Object.entries(finalFormData)
      .filter(
        ([key]) =>
          !key.startsWith('custom_') &&
          !reservedFields.includes(key.toLowerCase()),
      )
      .reduce(
        (acc, [key, value]) => {
          acc[key] = value;
          return acc;
        },
        {} as Record<string, any>,
      );

    return { customFieldValues, standardFields };
  }

  /**
   * Process custom fields in the form data
   */
  private processCustomFields(
    finalFormData: Record<string, any>,
    fields: Field[],
  ) {
    return Object.entries(finalFormData)
      .filter(([key]) => key.startsWith('custom_'))
      .map(([key, value]) => {
        const customFieldId = key.replace('custom_', '');
        const originalField = fields.find((f) => f.id === customFieldId);
        const isDateTimeField =
          originalField &&
          (originalField.type.toLowerCase() === 'date_time' ||
            originalField.type.toLowerCase() === 'datetime');

        // Special case for empty CFTTWQ55ZYY field
        if (customFieldId === 'CFTTWQ55ZYY' && (!value || value === '')) {
          return { customFieldId, data: [{ value: '' }], metadata: {} };
        }

        if (isDateTimeField) {
          return this.processDateTimeCustomField(customFieldId, value);
        }

        // Process regular custom field
        return this.processRegularCustomField(
          customFieldId,
          value,
          isDateTimeField,
        );
      });
  }

  /**
   * Process a datetime custom field
   */
  private processDateTimeCustomField(customFieldId: string, value: any) {
    this.logger.debug(
      `${LOG_SPAN} Processing datetime custom field ${customFieldId} with value: ${value} (type: ${typeof value})`,
    );

    if (value === '' || value === null || value === undefined) {
      this.logger.debug(
        `${LOG_SPAN} Empty value for datetime custom field ${customFieldId}, returning empty string`,
      );
      return { customFieldId, data: [{ value: '' }], metadata: {} };
    }

    try {
      // Ensure the date is interpreted as UTC
      let date: Date;
      const originalValue = String(value);

      if (typeof value === 'string' && !value.endsWith('Z')) {
        // If the string doesn't end with Z, append it to ensure UTC interpretation
        const dateString = `${value}Z`;
        this.logger.debug(
          `${LOG_SPAN} Appending Z to ensure UTC interpretation: ${dateString}`,
        );
        date = new Date(dateString);
      } else {
        this.logger.debug(
          `${LOG_SPAN} Using value as-is for datetime: ${value}`,
        );
        date = new Date(value as string | number | Date);
      }

      if (Number.isNaN(date.getTime())) {
        this.logger.error(
          `${LOG_SPAN} Invalid date format for custom field ${customFieldId}: ${originalValue}`,
        );
        return { customFieldId, data: [{ value: '' }], metadata: {} };
      }

      const isoString = date.toISOString().split('.')[0];

      this.logger.debug(
        `${LOG_SPAN} Converted datetime to UTC: ${isoString} for custom field ${customFieldId}`,
      );

      return {
        customFieldId,
        data: [{ value: isoString }],
        metadata: {},
      };
    } catch (err) {
      this.logger.error(
        `${LOG_SPAN} Error formatting datetime for ${customFieldId} (value: ${value}): ${err instanceof Error ? err.message : String(err)}`,
      );
      return { customFieldId, data: [{ value: '' }], metadata: {} };
    }
  }

  /**
   * Process a regular (non-datetime) custom field
   */
  private processRegularCustomField(
    customFieldId: string,
    value: any,
    isDateTimeField: boolean,
  ) {
    const processValue = (val: any) =>
      isDateTimeField && (val === null || val === undefined)
        ? ''
        : val === null || val === undefined
          ? ''
          : String(val);

    return {
      customFieldId,
      data: Array.isArray(value)
        ? value.map((v) => ({ value: processValue(v) }))
        : [{ value: processValue(value) }],
      metadata: {},
    };
  }

  /**
   * Creates a ticket with the processed form data
   */
  private async createTicket(
    viewAction: ViewSubmissionPayload,
    metadata: any,
    installation: any,
    finalFormData: Record<string, any>,
    customFieldValues: any[],
    standardFields: Record<string, any>,
    channelId: string,
    teamId: string,
    formId: string,
    client: any,
  ) {
    try {
      const userId = viewAction.user?.id || '';
      const slackTeamId = viewAction.team?.id || installation.teamId;
      const messageTs = metadata.ts || metadata.messageTs || metadata.threadTs;
      const fallbackTs = `${Math.floor(Date.now() / 1000)}.${Date.now() % 1000}`;
      const slackTs = messageTs || fallbackTs;

      this.logger.debug(
        `${LOG_SPAN} Context: User ID: ${userId}, Channel ID: ${channelId}, Team ID: ${slackTeamId}, Timestamp: ${slackTs} (${messageTs ? 'original' : 'fallback'})`,
      );

      // Check if ticket exists (dummy check kept for compatibility)
      const ticketExists = false;
      if (ticketExists) {
        return await this.handleExistingTicket(
          client,
          channelId,
          userId,
          { id: 'dummy-id', title: 'Dummy Ticket' },
          metadata.threadTs || slackTs,
          teamId,
        );
      }

      // Create the ticket payload
      const createTicketPayload = {
        teamId: teamId,
        requestorEmail: finalFormData.requestorEmail,
        title: finalFormData.title,
        description: finalFormData.description || '',
        priorityId: finalFormData.priorityId || finalFormData.priority, // Handle both forms
        metadata: {
          slack: { ts: slackTs, user: userId, channel: channelId },
          slackTeamId: slackTeamId,
        },
        text: finalFormData.description || '',
        formId,
        customFieldValues,
        ...standardFields,
      };

      this.logger.debug(
        `${LOG_SPAN} Creating new ticket with payload:`,
        JSON.stringify(createTicketPayload, null, 2),
      );

      // Create the ticket using the createNewTicket method
      let ticket: Ticket;
      try {
        ticket = await this.thenaPlatformApiProvider.createNewTicket(
          installation,
          createTicketPayload,
        );
      } catch (error) {
        this.logger.error(
          `${LOG_SPAN} Error creating ticket via provider: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        throw error;
      }

      // Convert the ticket to the expected format
      const newTicket = {
        id: ticket.id,
        title: ticket.title,
        teamIdentifier: ticket.teamIdentifier,
        ticketId: String(ticket.ticketId),
        status: ticket.status,
        statusId: ticket.statusId,
        priority: ticket.priority,
        priorityId: ticket.priorityId,
        teamId: ticket.teamId,
      };

      // Send confirmation or add comment
      await this.handleTicketCreationSuccess(
        installation,
        newTicket,
        channelId,
        slackTs,
        userId,
        fallbackTs,
        teamId,
        client,
        finalFormData,
        metadata,
      );

      return { response_action: 'clear' };
    } catch (error) {
      return this.handleTicketCreationError(error);
    }
  }

  /**
   * Handles the case where a ticket already exists
   */
  private async handleExistingTicket(
    client: any,
    channelId: string,
    userId: string,
    existingTicket: any,
    threadTs: string,
    teamId: string,
  ) {
    this.logger.log(
      `${LOG_SPAN} Ticket already exists with ID: ${existingTicket.id}`,
    );
    try {
      await this.notificationService.sendTicketAlreadyExistsNotification(
        client,
        channelId,
        userId,
        existingTicket,
        threadTs,
        teamId,
      );
    } catch (msgError) {
      this.logger.error(
        `${LOG_SPAN} Error sending existing ticket notification: ${msgError}`,
      );
    }
    return { response_action: 'clear' };
  }

  /**
   * Handles successful ticket creation
   */
  private async handleTicketCreationSuccess(
    installation: any,
    newTicket: TicketData,
    channelId: string,
    slackTs: string,
    userId: string,
    fallbackTs: string,
    teamId: string,
    client: any,
    finalFormData: Record<string, any>,
    metadata: any,
  ) {
    try {
      const threadTs = metadata.threadTs || slackTs;
      if (threadTs && threadTs !== 'SLASH_TICKET' && threadTs !== fallbackTs) {
        await this.addMessageAsComment(
          installation,
          newTicket,
          channelId,
          threadTs,
          userId,
        );
      } else {
        await this.notificationService.sendTicketCreationConfirmation(
          client,
          channelId,
          userId,
          {
            id: newTicket.id,
            title: newTicket.title || finalFormData.title,
            teamIdentifier: newTicket.teamIdentifier,
            ticketId: newTicket.ticketId,
          },
          threadTs,
          teamId,
        );
      }
    } catch (msgError) {
      this.logger.error(
        `${LOG_SPAN} Error sending success message: ${msgError}`,
      );
    }
  }

  /**
   * Handles errors during ticket creation
   */
  private handleTicketCreationError(error: any) {
    this.logger.error(
      `${LOG_SPAN} Error creating ticket: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );

    // Map error to a field if possible, or use a generic block_id
    const firstFieldId = 'block_0';
    return {
      response_action: 'errors',
      errors: {
        [firstFieldId]:
          error instanceof Error ? error.message : 'Failed to create ticket',
      },
    };
  }

  /**
   * Adds a message as a comment to a ticket
   */
  private async addMessageAsComment(
    installation: any,
    ticket: TicketData,
    channelId: string,
    messageTs: string,
    userId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `${LOG_SPAN} Adding message as comment for ticket ${ticket.id} from channel ${channelId}, message ${messageTs}`,
      );

      // Find the channel
      const slackChannel = await this.channelsRepository.findByCondition({
        where: {
          channelId: channelId,
          installation: { id: installation.id },
        },
      });

      if (!slackChannel) {
        this.logger.error(
          `${LOG_SPAN} Channel not found for team: ${installation.teamId}`,
        );
        return;
      }

      // Get the message history
      const messageHistory = await this.slackApiProvider.getConversationHistory(
        installation.botToken,
        { channel: channelId, latest: messageTs, limit: 1, inclusive: true },
      );

      if (!messageHistory.ok) {
        this.logger.error(
          `${LOG_SPAN} Error getting slack message history`,
          messageHistory.error,
        );
        return;
      }

      const slackMessage = messageHistory.messages[0];
      if (!slackMessage) {
        this.logger.error(
          `${LOG_SPAN} No message found with timestamp ${messageTs}`,
        );
        return;
      }

      // Get message permalink
      const permalinkResponse = await this.slackApiProvider.getPermalink(
        installation.botToken,
        { channel: channelId, message_ts: messageTs },
      );

      if (!permalinkResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} Error getting slack message permalink`,
          permalinkResponse.error,
        );
        return;
      }
      const permalink = permalinkResponse?.permalink;

      // Get message user
      const messageUser =
        await this.slackAppManagementService.upsertPersonWithIdentification(
          slackMessage.user || userId,
          installation,
          slackChannel,
        );

      // Create comment in transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Create slack message record
        const slackMessageRecord =
          await this.slackMessagesRepository.saveWithTxn(txnContext, {
            channel: { id: slackChannel.id },
            platformTicketId: ticket.id,
            slackPermalink: permalink,
            slackMessageTs: slackMessage.ts,
            slackMessageThreadTs: slackMessage.thread_ts,
            slackUserId: slackMessage.user || userId,
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
            metadata: {
              ticket_details: {
                status: ticket.status,
                statusId: ticket.statusId,
                priority: ticket.priority,
                priorityId: ticket.priorityId,
              },
            },
          });

        // Create comment payload
        const commentPayload: CreateNewComment = {
          channelId: channelId,
          content: slackMessage.text,
          files: [],
          impersonatedUserAvatar: messageUser.getUserAvatar(),
          impersonatedUserEmail: messageUser.slackProfileEmail,
          impersonatedUserName: messageUser.displayName || messageUser.realName,
          ticketId: ticket.id,
          commentVisibility: 'public',
          metadata: {
            ignoreSelf: true,
            ts: slackMessage.ts,
            threadTs: messageTs,
          },
        };

        if (messageUser instanceof CustomerContacts) {
          commentPayload.customerEmail = messageUser.slackProfileEmail;
        }

        // Create comment
        const comment = await this.thenaPlatformApiProvider.createNewComment(
          installation,
          commentPayload,
        );

        // Update message with comment ID
        await this.slackMessagesRepository.updateWithTxn(
          txnContext,
          slackMessageRecord.id,
          { platformCommentId: comment.data.id },
        );

        // Create comment thread mapping
        await this.commentThreadMapsRepository.saveWithTxn(txnContext, {
          organization: installation.organization,
          platformCommentThreadId: comment.data.id,
          platformCommentTicketId: ticket.id,
          slackThreadId: messageTs,
          slackChannelId: channelId,
        });

        // Send confirmation message
        const platformUrl = this.configService.get(ConfigKeys.THENA_WEB_URL);
        const ticketUrl = `${platformUrl}/dashboard/${ticket.teamId}?ticketId=${ticket.id}`;
        await this.slackApiProvider.sendMessage(installation.botToken, {
          channel: channelId,
          text: `<${ticketUrl}|${ticket.teamIdentifier || ''}#${ticket.ticketId || ticket.id}>: Ticket created successfully`,
          thread_ts: messageTs,
          unfurl_links: true,
          unfurl_media: true,
        });
      });

      this.logger.log(
        `${LOG_SPAN} Successfully added message as comment for ticket ${ticket.id}`,
      );
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} Error adding message as comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Generic error logger
   */
  private logError(error: unknown): void {
    if (error instanceof Error) {
      this.logger.error(
        `${LOG_SPAN} Error processing form submission: ${error.message}`,
      );
    } else {
      this.logger.error(
        `${LOG_SPAN} Error processing form submission: ${JSON.stringify(error)}`,
      );
    }
  }
}
